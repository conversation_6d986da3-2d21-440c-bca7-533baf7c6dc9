(function() {
  // 防止重复注入
  if (window.__subtitleListenerInjected) return;
  window.__subtitleListenerInjected = true;

  console.log('🎯 YouTube字幕监听器已注入');

  // 存储监听到的字幕URL
  let capturedSubtitleUrls = [];
  let hasTriggeredSubtitleButton = false;
  let selectedLanguage = 'english'; // 默认语言
  let trilingualUrls = {}; // 存储生成的三语言URL（中英日）
  let isProcessing = false; // 处理状态标记
  let processedUrls = new Set(); // 已处理的URL集合
  let urlValidationCache = new Map(); // URL验证结果缓存
  let lastRequestTime = 0; // 上次请求时间
  const REQUEST_INTERVAL = 3000; // 请求间隔（毫秒）

  // 检测是否为timedtext请求
  function isTimedTextRequest(url) {
    return url && url.includes('timedtext');
  }

  // 请求限流：确保请求间隔
  async function throttleRequest() {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;

    if (timeSinceLastRequest < REQUEST_INTERVAL) {
      const waitTime = REQUEST_INTERVAL - timeSinceLastRequest;
      console.log(`⏱️ 请求限流，等待 ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    lastRequestTime = Date.now();
  }

  // 读取插件语言设置
  async function getSelectedLanguage() {
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
          const language = result.selectedVideoLanguage || 'english';
          resolve(language);
        });
      } else {
        resolve('english'); // 默认值
      }
    });
  }

  // 语言代码映射
  function getLanguageCode(language) {
    const languageMap = {
      'english': 'en',
      'japanese': 'ja'
    };
    return languageMap[language] || 'en';
  }

  // 获取轨道信息
  function getCaptionTracks() {
    try {
      const playerResponse = window.ytInitialPlayerResponse;
      const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
      const audioTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.audioTracks || [];

      console.log(`📋 找到 ${captionTracks.length} 个字幕轨道`);

      return { captionTracks, audioTracks };
    } catch (error) {
      console.error('❌ 获取轨道信息失败:', error);
      return { captionTracks: [], audioTracks: [] };
    }
  }

  // 检查语言代码是否匹配（支持变体）
  function isLanguageMatch(trackLanguageCode, targetLanguageCode) {
    if (!trackLanguageCode || !targetLanguageCode) return false;

    // 完全匹配
    if (trackLanguageCode === targetLanguageCode) return true;

    // 检查语言变体（如 en-US 匹配 en）
    const trackLangBase = trackLanguageCode.split('-')[0];
    const targetLangBase = targetLanguageCode.split('-')[0];

    return trackLangBase === targetLangBase;
  }

  // 获取语言变体优先级列表
  function getLanguageVariants(languageCode) {
    const variants = {
      'en': ['en-US', 'en-GB', 'en', 'en-CA', 'en-AU', 'en-IN'],
      'ja': ['ja', 'ja-JP'],
      'zh': ['zh-Hans', 'zh', 'zh-Hant', 'zh-CN', 'zh-TW']
    };

    return variants[languageCode] || [languageCode];
  }

  // 查找最佳字幕轨道
  function findBestSubtitleTrack(captionTracks, targetLanguageCode, isChineseTrack = false, isJapaneseTrack = false) {
    if (isChineseTrack) {
      // 中文字幕优先级：zh-Hans > zh > zh-Hant > zh-CN > zh-TW
      const chineseVariants = getLanguageVariants('zh');

      for (const variant of chineseVariants) {
        // 优先查找用户上传的字幕
        const userTrack = captionTracks.find(track =>
          track.languageCode === variant && !track.kind
        );
        if (userTrack) {
          console.log(`✅ 找到用户上传的中文字幕: ${variant}`);
          return { track: userTrack, type: 'user' };
        }
      }

      // 如果没有用户上传的中文字幕，查找可翻译的轨道用于自动翻译
      const translatableTrack = captionTracks.find(track => track.isTranslatable);
      if (translatableTrack) {
        console.log('✅ 找到可翻译轨道，将使用自动翻译中文字幕');
        return { track: translatableTrack, type: 'translate' };
      }
    } else if (isJapaneseTrack) {
      // 日文字幕处理逻辑
      const japaneseVariants = getLanguageVariants('ja');

      // 优先查找用户上传的日文字幕
      for (const variant of japaneseVariants) {
        const userTrack = captionTracks.find(track =>
          isLanguageMatch(track.languageCode, variant) && !track.kind
        );
        if (userTrack) {
          console.log(`✅ 找到用户上传的日文字幕: ${userTrack.languageCode}`);
          return { track: userTrack, type: 'user' };
        }
      }

      // 查找自动生成的日文字幕
      for (const variant of japaneseVariants) {
        const asrTrack = captionTracks.find(track =>
          isLanguageMatch(track.languageCode, variant) && track.kind === 'asr'
        );
        if (asrTrack) {
          console.log(`✅ 找到自动生成的日文字幕: ${asrTrack.languageCode}`);
          return { track: asrTrack, type: 'asr' };
        }
      }

      // 如果没有找到日文字幕，查找可翻译的轨道用于自动翻译
      const translatableTrack = captionTracks.find(track => track.isTranslatable);
      if (translatableTrack) {
        console.log('✅ 找到可翻译轨道，将使用自动翻译日文字幕');
        return { track: translatableTrack, type: 'translate' };
      }
    } else {
      // 目标语言字幕查找，支持语言变体
      const languageVariants = getLanguageVariants(targetLanguageCode);

      // 优先查找用户上传的字幕
      for (const variant of languageVariants) {
        const userTrack = captionTracks.find(track =>
          isLanguageMatch(track.languageCode, variant) && !track.kind
        );
        if (userTrack) {
          console.log(`✅ 找到用户上传的${targetLanguageCode}字幕: ${userTrack.languageCode}`);
          return { track: userTrack, type: 'user' };
        }
      }

      // 查找自动生成的字幕
      for (const variant of languageVariants) {
        const asrTrack = captionTracks.find(track =>
          isLanguageMatch(track.languageCode, variant) && track.kind === 'asr'
        );
        if (asrTrack) {
          console.log(`✅ 找到自动生成的${targetLanguageCode}字幕: ${asrTrack.languageCode}`);
          return { track: asrTrack, type: 'asr' };
        }
      }
    }

    return null;
  }

  // 生成字幕URL
  function generateSubtitleUrl(baseUrl, trackInfo, targetLanguageCode, isChineseTrack = false, isJapaneseTrack = false) {
    try {
      const url = new URL(baseUrl);

      if (isChineseTrack) {
        if (trackInfo.type === 'user') {
          // 用户上传的中文字幕，替换lang参数，使用实际的语言代码
          url.searchParams.set('lang', trackInfo.track.languageCode);
          // 清除可能存在的tlang参数
          url.searchParams.delete('tlang');
        } else if (trackInfo.type === 'translate') {
          // 自动翻译的中文字幕，添加tlang参数
          url.searchParams.set('tlang', 'zh-Hans');
        }
      } else if (isJapaneseTrack) {
        if (trackInfo.type === 'user') {
          // 用户上传的日文字幕，替换lang参数，使用实际的语言代码
          url.searchParams.set('lang', trackInfo.track.languageCode);
          // 清除可能存在的tlang参数
          url.searchParams.delete('tlang');
        } else if (trackInfo.type === 'asr') {
          // 自动生成的日文字幕
          url.searchParams.set('lang', trackInfo.track.languageCode);
          url.searchParams.set('kind', 'asr');
          // 清除可能存在的tlang参数
          url.searchParams.delete('tlang');
        } else if (trackInfo.type === 'translate') {
          // 自动翻译的日文字幕，添加tlang参数
          url.searchParams.set('tlang', 'ja');
        }
      } else {
        // 其他目标语言字幕，使用实际找到的语言代码
        url.searchParams.set('lang', trackInfo.track.languageCode);
        // 清除可能存在的tlang参数，确保获取原始字幕而非翻译
        url.searchParams.delete('tlang');
        if (trackInfo.type === 'asr') {
          // 自动生成的字幕，添加kind=asr
          url.searchParams.set('kind', 'asr');
        }
      }

      return url.toString();
    } catch (error) {
      console.error('❌ 生成字幕URL失败:', error);
      return null;
    }
  }

  // 验证字幕URL是否有效
  async function validateSubtitleUrl(url, silent = false) {
    try {
      if (!silent) console.log('🔍 验证字幕URL有效性...');

      // 检查缓存
      if (urlValidationCache.has(url)) {
        const cachedResult = urlValidationCache.get(url);
        if (!silent) console.log('📋 使用缓存的验证结果:', cachedResult);
        return cachedResult;
      }

      // 请求限流
      await throttleRequest();

      // 构建请求头，模拟浏览器请求
      const headers = {
        'User-Agent': navigator.userAgent,
        'Referer': window.location.href,
        'Origin': window.location.origin,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': navigator.language || 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      const response = await fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'include', // 包含Cookie
        cache: 'no-cache'
      });

      if (!response.ok) {
        const errorMsg = `HTTP ${response.status} ${response.statusText}`;
        if (!silent) console.warn('⚠️ 字幕URL响应失败:', errorMsg);

        // 缓存失败结果（较短时间）
        urlValidationCache.set(url, false);
        setTimeout(() => urlValidationCache.delete(url), 30000); // 30秒后清除缓存

        return false;
      }

      const data = await response.json();

      // 检查是否有字幕内容
      if (data && data.events && Array.isArray(data.events) && data.events.length > 0) {
        // 检查是否有实际的文本内容
        const hasContent = data.events.some(event =>
          event.segs && event.segs.some(seg => seg.utf8 && seg.utf8.trim())
        );

        if (hasContent) {
          if (!silent) console.log('✅ 字幕URL验证成功');

          // 缓存成功结果
          urlValidationCache.set(url, true);
          setTimeout(() => urlValidationCache.delete(url), 300000); // 5分钟后清除缓存

          return true;
        } else {
          if (!silent) console.warn('⚠️ 字幕URL无有效文本内容');

          // 缓存失败结果
          urlValidationCache.set(url, false);
          setTimeout(() => urlValidationCache.delete(url), 60000); // 1分钟后清除缓存

          return false;
        }
      } else {
        if (!silent) console.warn('⚠️ 字幕URL无events数据或为空');

        // 缓存失败结果
        urlValidationCache.set(url, false);
        setTimeout(() => urlValidationCache.delete(url), 60000); // 1分钟后清除缓存

        return false;
      }
    } catch (error) {
      if (!silent) console.error('❌ 验证字幕URL时出错:', error);

      // 对于网络错误，不缓存结果，允许重试
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        if (!silent) console.log('🔄 网络错误，不缓存结果，允许重试');
      } else {
        // 其他错误缓存较短时间
        urlValidationCache.set(url, false);
        setTimeout(() => urlValidationCache.delete(url), 30000);
      }

      return false;
    }
  }

  // 处理监听到的字幕请求
  async function handleSubtitleRequest(url, method = 'GET') {
    if (isTimedTextRequest(url)) {
      // 检查是否已经处理过这个URL或正在处理中
      if (processedUrls.has(url) || isProcessing) {
        return;
      }

      console.log('🟡 拦截到字幕请求:', url.substring(0, 100) + '...');

      // 存储URL避免重复
      if (!capturedSubtitleUrls.includes(url)) {
        capturedSubtitleUrls.push(url);
        processedUrls.add(url);
        console.log('📝 新字幕URL已记录');

        // 验证URL并生成三语言URL
        await validateAndGenerateTrilingualUrls(url);
      }
    }
  }

  // 验证URL并生成三语言URL
  async function validateAndGenerateTrilingualUrls(baseUrl, retryCount = 0) {
    const maxRetries = 2;

    // 防止重复处理
    if (isProcessing) {
      return;
    }

    isProcessing = true;

    try {
      console.log('🔄 开始处理字幕URL...');

      // 首先验证基础URL是否有效
      const isValid = await validateSubtitleUrl(baseUrl, true);

      if (!isValid) {
        console.warn(`⚠️ 字幕URL无效 (尝试 ${retryCount + 1}/${maxRetries + 1})`);

        if (retryCount < maxRetries) {
          console.log('🔄 尝试重新获取字幕URL...');

          // 清除已处理标记，允许处理新URL
          processedUrls.clear();

          // 强制重新触发字幕按钮（即使已激活也要重新触发）
          clickSubtitleButton(true);

          // 重置处理状态，等待新URL
          isProcessing = false;
          return;
        } else {
          console.error('❌ 多次尝试后仍无法获取有效的字幕URL');
          isProcessing = false;
          return;
        }
      }

      // URL有效，继续生成三语言URL
      await generateTrilingualUrls(baseUrl);

    } catch (error) {
      console.error('❌ 验证并生成双语字幕URL时出错:', error);
    } finally {
      isProcessing = false;
    }
  }

  // 生成三语言URL（中英日）
  async function generateTrilingualUrls(baseUrl) {
    try {
      console.log('🔄 生成三语言字幕URL（中英日）...');

      // 获取轨道信息
      const { captionTracks } = getCaptionTracks();

      if (captionTracks.length === 0) {
        console.warn('⚠️ 未找到任何字幕轨道');
        return;
      }

      // 查找中文字幕轨道
      const chineseTrackInfo = findBestSubtitleTrack(captionTracks, null, true);
      if (!chineseTrackInfo) {
        console.error('❌ 未找到中文字幕轨道');
        return;
      }

      // 查找英文字幕轨道
      const englishTrackInfo = findBestSubtitleTrack(captionTracks, 'en', false);
      if (!englishTrackInfo) {
        console.warn('⚠️ 未找到英文字幕轨道');
      }

      // 查找日文字幕轨道
      const japaneseTrackInfo = findBestSubtitleTrack(captionTracks, 'ja', false, true);
      if (!japaneseTrackInfo) {
        console.warn('⚠️ 未找到日文字幕轨道');
      }

      // 生成URL
      const urls = {};

      // 生成中文字幕URL
      const chineseUrl = generateSubtitleUrl(baseUrl, chineseTrackInfo, null, true);
      if (chineseUrl && await validateSubtitleUrl(chineseUrl, true)) {
        urls.chinese = {
          language: 'chinese',
          languageCode: chineseTrackInfo.track.languageCode,
          url: chineseUrl,
          type: chineseTrackInfo.type
        };
      }

      // 生成英文字幕URL（添加延迟避免429错误）
      if (englishTrackInfo) {
        const englishUrl = generateSubtitleUrl(baseUrl, englishTrackInfo, 'en', false, false);
        if (englishUrl && await validateSubtitleUrl(englishUrl, true)) {
          urls.english = {
            language: 'english',
            languageCode: englishTrackInfo.track.languageCode,
            url: englishUrl,
            type: englishTrackInfo.type
          };
        }
      }

      // 生成日文字幕URL（添加延迟避免429错误）
      if (japaneseTrackInfo) {
        const japaneseUrl = generateSubtitleUrl(baseUrl, japaneseTrackInfo, 'ja', false, true);
        if (japaneseUrl && await validateSubtitleUrl(japaneseUrl, true)) {
          urls.japanese = {
            language: 'japanese',
            languageCode: japaneseTrackInfo.track.languageCode,
            url: japaneseUrl,
            type: japaneseTrackInfo.type
          };
        }
      }

      // 统计生成结果
      const generatedCount = Object.keys(urls).length;
      const chineseStatus = urls.chinese ? '✅' : '❌';
      const englishStatus = urls.english ? '✅' : '❌';
      const japaneseStatus = urls.japanese ? '✅' : '❌';

      if (generatedCount > 0) {
        trilingualUrls = urls;
        console.log(`🎉 字幕URL生成完成！成功生成 ${generatedCount}/3 种语言`);
        console.log(`🇨🇳 中文字幕: ${chineseStatus} ${urls.chinese?.url || 'undefined'}`);
        console.log(`🇺🇸 英文字幕: ${englishStatus} ${urls.english?.url || 'undefined'}`);
        console.log(`🇯🇵 日文字幕: ${japaneseStatus} ${urls.japanese?.url || 'undefined'}`);

        // 显示字幕类型信息
        if (urls.chinese) {
          console.log(`   中文字幕类型: ${urls.chinese.type === 'user' ? '用户上传' : urls.chinese.type === 'translate' ? '自动翻译' : '自动生成'}`);
        }
        if (urls.english) {
          console.log(`   英文字幕类型: ${urls.english.type === 'user' ? '用户上传' : urls.english.type === 'asr' ? '自动生成' : '自动翻译'}`);
        }
        if (urls.japanese) {
          console.log(`   日文字幕类型: ${urls.japanese.type === 'user' ? '用户上传' : urls.japanese.type === 'asr' ? '自动生成' : '自动翻译'}`);
        }
      } else {
        console.error('❌ 所有语言的字幕URL生成都失败了');
      }

    } catch (error) {
      console.error('❌ 生成三语言字幕URL时出错:', error);
    }
  }

  // Hook XMLHttpRequest
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    this._isTimedText = isTimedTextRequest(url);
    this._url = url;
    this._method = method;
    return originalXHROpen.apply(this, arguments);
  };

  XMLHttpRequest.prototype.send = function(body) {
    if (this._isTimedText) {
      const xhr = this;
      this.addEventListener("load", function() {
        handleSubtitleRequest(xhr._url, xhr._method);
      });
    }
    return originalXHRSend.apply(this, arguments);
  };

  // Hook fetch API
  const originalFetch = window.fetch;
  window.fetch = function(input, init) {
    const url = typeof input === 'string' ? input : (input && input.url ? input.url : '');
    const method = init && init.method ? init.method : 'GET';

    if (isTimedTextRequest(url)) {
      handleSubtitleRequest(url, method);
    }

    return originalFetch.apply(this, arguments);
  };

  // 模拟点击字幕按钮
  function clickSubtitleButton(forceRetrigger = false) {
    try {
      const subtitleButton = document.querySelector('.ytp-subtitles-button.ytp-button');
      if (subtitleButton) {
        // 检查按钮是否已激活
        const isActive = subtitleButton.getAttribute('aria-pressed') === 'true' ||
                        subtitleButton.classList.contains('ytp-button-active');

        if (!isActive) {
          subtitleButton.click();
          console.log('✅ 已模拟点击字幕按钮');
          hasTriggeredSubtitleButton = true;
          return true;
        } else if (forceRetrigger) {
          // 强制重新触发：先取消激活，然后重新激活
          console.log('🔄 字幕按钮已激活，强制重新触发...');

          // 第一次点击：取消激活
          subtitleButton.click();
          console.log('🔸 第一次点击：取消字幕激活');

          // 等待一小段时间确保状态变更
          setTimeout(() => {
            // 第二次点击：重新激活
            subtitleButton.click();
            console.log('🔸 第二次点击：重新激活字幕');
            hasTriggeredSubtitleButton = true;
          }, 300); // 300ms延迟确保状态切换完成

          return true;
        } else {
          console.log('ℹ️ 字幕按钮已处于激活状态');
          return true;
        }
      } else {
        console.warn('❌ 未找到字幕按钮（可能当前视频不支持字幕或页面尚未加载完成）');
        return false;
      }
    } catch (error) {
      console.error('❌ 点击字幕按钮时出错:', error);
      return false;
    }
  }

  // 检查是否需要主动触发字幕按钮
  function checkAndTriggerSubtitleButton() {
    // 如果已经监听到字幕请求，就不需要点击按钮了
    if (capturedSubtitleUrls.length > 0) {
      console.log('✅ 已监听到字幕请求，无需点击按钮');
      return;
    }

    // 如果已经尝试过点击按钮，也不再重复
    if (hasTriggeredSubtitleButton) {
      console.log('ℹ️ 已尝试过点击字幕按钮');
      return;
    }

    console.log('🔍 未监听到字幕请求，尝试点击字幕按钮...');
    clickSubtitleButton();
  }

  // 获取当前监听到的字幕URL列表
  function getCapturedSubtitleUrls() {
    return [...capturedSubtitleUrls];
  }

  // 清空已监听的URL列表
  function clearCapturedUrls() {
    capturedSubtitleUrls = [];
    hasTriggeredSubtitleButton = false;
    trilingualUrls = {};
    isProcessing = false;
    processedUrls.clear();
    urlValidationCache.clear();
    lastRequestTime = 0;
    console.log('🧹 已清空字幕URL记录和缓存');
  }

  // 获取生成的三语言URL
  function getTrilingualUrls() {
    return { ...trilingualUrls };
  }

  // 获取双语URL（向后兼容）
  function getBilingualUrls() {
    // 获取当前选择的语言
    const currentLanguage = selectedLanguage || 'english';

    // 返回当前语言和中文的双语组合
    const result = {};

    if (trilingualUrls.chinese) {
      result.chinese = trilingualUrls.chinese;
    }

    if (currentLanguage === 'english' && trilingualUrls.english) {
      result.target = trilingualUrls.english;
    } else if (currentLanguage === 'japanese' && trilingualUrls.japanese) {
      result.target = trilingualUrls.japanese;
    }

    return result;
  }

  // 暴露API到全局
  window.SubtitleListener = {
    getCapturedUrls: getCapturedSubtitleUrls,
    clearCapturedUrls: clearCapturedUrls,
    clickSubtitleButton: clickSubtitleButton,
    checkAndTrigger: checkAndTriggerSubtitleButton,
    getBilingualUrls: getBilingualUrls,
    getTrilingualUrls: getTrilingualUrls,
    generateTrilingualUrls: generateTrilingualUrls,
    validateSubtitleUrl: validateSubtitleUrl,
    validateAndGenerateTrilingualUrls: validateAndGenerateTrilingualUrls,
    // 向后兼容的方法名
    generateBilingualUrls: generateTrilingualUrls,
    validateAndGenerateBilingualUrls: validateAndGenerateTrilingualUrls
  };

  // 监听来自content script的消息
  window.addEventListener('message', function(event) {
    // 只处理来自同一页面的消息
    if (event.source !== window) return;

    const message = event.data;
    if (!message || message.type !== 'SUBTITLE_LISTENER_REQUEST') return;

    console.log('🔔 收到字幕监听器请求:', message.action);

    let response = {
      type: 'SUBTITLE_LISTENER_RESPONSE',
      requestId: message.requestId,
      success: false,
      data: null,
      error: null
    };

    try {
      switch (message.action) {
        case 'getBilingualUrls':
          response.data = getBilingualUrls();
          response.success = true;
          break;

        case 'getTrilingualUrls':
          response.data = getTrilingualUrls();
          response.success = true;
          break;

        case 'getCapturedUrls':
          response.data = getCapturedSubtitleUrls();
          response.success = true;
          break;

        case 'clickSubtitleButton':
          response.data = clickSubtitleButton();
          response.success = true;
          break;

        case 'checkStatus':
          response.data = {
            isAvailable: true,
            capturedUrls: capturedSubtitleUrls.length,
            hasBilingualUrls: Object.keys(getBilingualUrls()).length > 0,
            hasTrilingualUrls: Object.keys(trilingualUrls).length > 0
          };
          response.success = true;
          break;

        default:
          response.error = `未知的操作: ${message.action}`;
      }
    } catch (error) {
      response.error = error.message;
      console.error('处理字幕监听器请求时出错:', error);
    }

    // 发送响应
    window.postMessage(response, '*');
  });

  // 页面加载完成后的检查逻辑
  function initializeListener() {
    console.log('🚀 字幕监听器初始化完成');
    
    // 延迟检查，给页面一些时间加载
    setTimeout(() => {
      checkAndTriggerSubtitleButton();
    }, 3000);

    // 定期检查（前30秒内每5秒检查一次）
    let checkCount = 0;
    const maxChecks = 6; // 30秒内检查6次
    
    const intervalId = setInterval(() => {
      checkCount++;
      
      if (capturedSubtitleUrls.length > 0 || checkCount >= maxChecks) {
        clearInterval(intervalId);
        if (capturedSubtitleUrls.length > 0) {
          console.log('✅ 字幕监听成功，停止定期检查');
        } else {
          console.log('⏰ 达到最大检查次数，停止定期检查');
        }
        return;
      }
      
      console.log(`🔄 第${checkCount}次定期检查字幕状态...`);
      checkAndTriggerSubtitleButton();
    }, 5000);
  }

  // 监听页面变化（YouTube是SPA应用）
  let lastUrl = window.location.href;
  function checkUrlChange() {
    const currentUrl = window.location.href;
    if (currentUrl !== lastUrl) {
      console.log('🔄 检测到页面URL变化，重置字幕监听器');
      lastUrl = currentUrl;
      clearCapturedUrls();

      // 延迟初始化，等待新页面加载
      setTimeout(initializeListener, 1000);
    }
  }

  // 监听URL变化
  setInterval(checkUrlChange, 1000);

  // 初始化
  if (document.readyState === 'complete') {
    setTimeout(initializeListener, 500);
  } else {
    window.addEventListener('load', () => {
      setTimeout(initializeListener, 500);
    });
  }

  console.log('✅ 已启用对 YouTube 字幕 timedtext 请求的监听');

})();
